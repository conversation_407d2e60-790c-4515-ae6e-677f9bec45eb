#Requires -Version 5.1
#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Creates symbolic links for the addon across multiple WoW installations.

.DESCRIPTION
    This script creates symbolic links for the addon in all World of Warcraft
    installation directories and sets appropriate attributes on reparse points.

.NOTES
    - Requires Administrator privileges
    - Will automatically request elevation if not running as Administrator
#>

[CmdletBinding()]
param()

# Configuration
$Config = @{
    ProjectPath = "E:\Projects\WoW Related\Paid Projects\Jiberish\SuperVillain"
    AddonPath = "Interface\AddOns"
    AddonName = "JiberishUI_SuperVillain"
    WoWInstallations = @{
        'Retail' = "D:\Program Files\World of Warcraft\_retail_"
        'MOP Pre-Patch' = "D:\Program Files\World of Warcraft\_classic_"
        'Classic Era' = "D:\Program Files\World of Warcraft\_classic_era_"
        'MoP Beta' = "D:\Program Files\World of Warcraft\_classic_beta_"
    }
}

function Test-Administrator {
    <#
    .SYNOPSIS
        Tests if the current session is running with Administrator privileges.
    #>
    $currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
    return $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Request-Elevation {
    <#
    .SYNOPSIS
        Requests elevation by restarting the script with Administrator privileges.
    #>
    $commandLine = "-NoProfile -ExecutionPolicy Bypass -File `"$($MyInvocation.ScriptName)`""
    if ($MyInvocation.UnboundArguments) {
        $commandLine += " " + ($MyInvocation.UnboundArguments -join " ")
    }

    try {
        Start-Process -FilePath "powershell.exe" -Verb RunAs -ArgumentList $commandLine -Wait
    }
    catch {
        Write-Error "Failed to elevate script: $_"
        exit 1
    }
    exit 0
}

function New-AddonSymlink {
    <#
    .SYNOPSIS
        Creates a symbolic link for the addon in the specified WoW installation.

    .PARAMETER InstallationName
        The name of the WoW installation (for logging purposes).

    .PARAMETER InstallationPath
        The path to the WoW installation directory.
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$InstallationName,

        [Parameter(Mandatory)]
        [string]$InstallationPath
    )

    $targetPath = Join-Path -Path $Config.ProjectPath -ChildPath $Config.AddonName -AdditionalChildPath $Config.AddonName
    $linkPath = Join-Path -Path $InstallationPath -ChildPath $Config.AddonPath -AdditionalChildPath $Config.AddonName

    # Verify source exists
    if (-not (Test-Path -Path $targetPath)) {
        Write-Warning "Source path does not exist: $targetPath"
        return $false
    }

    # Verify WoW installation exists
    if (-not (Test-Path -Path $InstallationPath)) {
        Write-Warning "$InstallationName installation not found: $InstallationPath"
        return $false
    }

    try {
        # Ensure the AddOns directory exists
        $addonDir = Join-Path -Path $InstallationPath -ChildPath $Config.AddonPath
        if (-not (Test-Path -Path $addonDir)) {
            New-Item -ItemType Directory -Path $addonDir -Force | Out-Null
        }

        # Create symbolic link
        New-Item -ItemType SymbolicLink -Path $linkPath -Target $targetPath -Force | Out-Null
        Write-Host "✓ Created symlink for $InstallationName" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Error "Failed to create symlink for $InstallationName`: $_"
        return $false
    }
}

function Set-ReparsePointAttributes {
    <#
    .SYNOPSIS
        Sets ReadOnly attributes on all reparse points in WoW AddOns directories.

    .PARAMETER InstallationPath
        The path to the WoW installation directory.
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$InstallationPath
    )

    $addonDir = Join-Path -Path $InstallationPath -ChildPath $Config.AddonPath

    if (-not (Test-Path -Path $addonDir)) {
        return
    }

    try {
        $reparsePoints = Get-ChildItem -Path $addonDir -Recurse -Force -ErrorAction SilentlyContinue |
            Where-Object { $_.PSIsContainer -and ($_.Attributes -band [System.IO.FileAttributes]::ReparsePoint) }

        foreach ($item in $reparsePoints) {
            $item.Attributes = [System.IO.FileAttributes]::ReparsePoint -bor [System.IO.FileAttributes]::ReadOnly
        }

        if ($reparsePoints.Count -gt 0) {
            Write-Host "  └─ Set attributes on $($reparsePoints.Count) reparse point(s)" -ForegroundColor Gray
        }
    }
    catch {
        Write-Warning "Failed to set reparse point attributes for $InstallationPath`: $_"
    }
}

# Main execution
try {
    # Check for Administrator privileges
    if (-not (Test-Administrator)) {
        Write-Host "Administrator privileges required. Requesting elevation..." -ForegroundColor Yellow
        Request-Elevation
    }

    Write-Host "Creating symbolic links for $($Config.AddonName)..." -ForegroundColor Cyan
    Write-Host ""

    $successCount = 0
    $totalCount = 0

    # Create symbolic links for each WoW installation
    foreach ($installation in $Config.WoWInstallations.GetEnumerator()) {
        $totalCount++
        if (New-AddonSymlink -InstallationName $installation.Key -InstallationPath $installation.Value) {
            $successCount++
        }
    }

    Write-Host ""
    Write-Host "Setting reparse point attributes..." -ForegroundColor Cyan

    # Set attributes on reparse points
    foreach ($installation in $Config.WoWInstallations.GetEnumerator()) {
        if (Test-Path -Path $installation.Value) {
            Write-Host "Processing $($installation.Key)..." -ForegroundColor Gray
            Set-ReparsePointAttributes -InstallationPath $installation.Value
        }
    }

    Write-Host ""
    Write-Host "Operation completed: $successCount/$totalCount installations processed successfully." -ForegroundColor $(if ($successCount -eq $totalCount) { 'Green' } else { 'Yellow' })

    if ($successCount -lt $totalCount) {
        Write-Host "Some operations failed. Check the warnings above for details." -ForegroundColor Yellow
        exit 1
    }
}
catch {
    Write-Error "Script execution failed: $_"
    exit 1
}
finally {
    # Pause if running interactively
    if ([Environment]::UserInteractive -and -not [Environment]::GetCommandLineArgs().Contains('-NonInteractive')) {
        Write-Host ""
        Write-Host "Press any key to continue..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey('NoEcho,IncludeKeyDown')
    }
}
