name: Release
on:
  push:
    tags:
      - '*'
    paths-ignore:
      - '.github/**'
jobs:
  github-action:
    runs-on: ubuntu-22.04
    env:
      CF_API_KEY: ${{ secrets.CF_API_KEY }}
      WAGO_API_TOKEN: ${{ secrets.WAGO_API_KEY }}
    steps:
      - name: Clone Project
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Package and Release
        uses: BigWigsMods/packager@master
        with:
          args: -n "{package-name}-{project-version}"
