## Interface: 110200, 50500, 11507
## Title: |cff1784d1JiberishUI|r |cffff5f00S|r|cffff6f00u|r|cffff7f00p|r|cffff8f00e|r|cffff9f00r|r|cffffd100V|r|cffff6f00i|r|cffff4f00l|r|cffff3f00l|r|cffff2f00a|r|cffff1f00i|r|cffff0f00n|r
## Author: Jiberish, Repooc
## Version: 1.0.0
## Notes: An |cff1784d1ElvUI|r Profile Installer that was put together by Repooc that allows you to setup JibiershUI's |cffff5f00S|r|cffff6f00u|r|cffff7f00p|r|cffff8f00e|r|cffff9f00r|r|cffffd100V|r|cffff6f00i|r|cffff4f00l|r|cffff3f00l|r|cffff2f00a|r|cffff1f00i|r|cffff0f00n|r profile with minimal effort.
## RequiredDeps: ElvUI
## OptionalDeps: ElvUI_EltreumUI, ElvUI_mMediaTag, ElvUI_JiberishIcons, BigWigs, Details, WeakAuras
## DefaultState: Enabled
## IconTexture: Interface\AddOns\JiberishUI_SuperVillain\Media\Logos\JiberishUILogo256x256
## SavedVariablesPerCharacter: JiberishUISuperVillainDB
## X-Curse-Project-ID: 1287060
## X-Wago-ID: x61zoEK1

Libs\Load_Libs.xml
Init.lua
Config.lua
AddOns\Load_AddOns.xml
Core\Load_Core.xml
