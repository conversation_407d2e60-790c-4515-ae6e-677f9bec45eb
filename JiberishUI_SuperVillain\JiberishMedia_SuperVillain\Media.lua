local LSM = _G.LibStub and _G.LibStub('LibSharedMedia-3.0')
if not _G.LibStub then return print("LibStub not found") end
if not LSM then return print("LibSharedMedia-3.0 not found") end

--* Backgrounds
LSM:Register("background", "ART1", [[Interface\AddOns\JiberishMedia_SuperVillain\Backgrounds\ART1.blp]])
LSM:Register("background", "ART2", [[Interface\AddOns\JiberishMedia_SuperVillain\Backgrounds\ART2.blp]])
LSM:Register("background", "ART3", [[Interface\AddOns\JiberishMedia_SuperVillain\Backgrounds\ART3.blp]])
LSM:Register("background", "ART4", [[Interface\AddOns\JiberishMedia_SuperVillain\Backgrounds\ART4.blp]])
LSM:Register("background", "ART5", [[Interface\AddOns\JiberishMedia_SuperVillain\Backgrounds\ART5.blp]])
LSM:Register("background", "ART6", [[Interface\AddOns\JiberishMedia_SuperVillain\Backgrounds\ART6.blp]])

--* Fonts
LSM:Register("font", "Alert",				[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Alert.ttf]])
LSM:Register("font", "Boom",				[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Boom.ttf]])
LSM:Register("font", "Carter",				[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Carter.ttf]])
LSM:Register("font", "Combat",				[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Combat.ttf]])
LSM:Register("font", "Combat2",				[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Combat2.ttf]])
LSM:Register("font", "Combo",				[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Combo.ttf]])
LSM:Register("font", "DejaVuSans",			[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\DejaVuSans.ttf]])
LSM:Register("font", "Dialog",				[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Dialog.ttf]])
LSM:Register("font", "Dyslexic",			[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Dyslexic.ttf]])
LSM:Register("font", "GothamNarrowUltra",	[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\GothamNarrowUltra.ttf]])
LSM:Register("font", "Invisible",			[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Invisible.ttf]])
LSM:Register("font", "Narrative",			[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Narrative.ttf]])
LSM:Register("font", "Numbers",				[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Numbers.ttf]])
LSM:Register("font", "Pixel",				[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Pixel.ttf]])
LSM:Register("font", "Poppins-Black",		[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Poppins-Black.ttf]])
LSM:Register("font", "Poppins-ExtraBold",	[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Poppins-ExtraBold.ttf]])
LSM:Register("font", "Poppins-Regular",		[[Interface\AddOns\JiberishMedia_SuperVillain\Fonts\Poppins-Regular.ttf]])

--* Icons
LSM:Register("icons", "CLOSE",			[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\CLOSE.blp]])
LSM:Register("icons", "Combat",			[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\Combat]])
LSM:Register("icons", "EXIT",			[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\EXIT.blp]])
LSM:Register("icons", "FAVORITE-STAR",	[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\FAVORITE-STAR.blp]])
LSM:Register("icons", "Hourglass",		[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\Hourglass.blp]])
LSM:Register("icons", "MOVE-DOWN",		[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\MOVE-DOWN.blp]])
LSM:Register("icons", "MOVE-LEFT",		[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\MOVE-LEFT.blp]])
LSM:Register("icons", "MOVE-RIGHT",		[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\MOVE-RIGHT.blp]])
LSM:Register("icons", "MOVE-UP",		[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\MOVE-UP.blp]])
LSM:Register("icons", "SVUI",			[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\SVUI.blp]])
LSM:Register("icons", "THEME",			[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\THEME.blp]])
LSM:Register("icons", "UNIT-AGGRO",		[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\UNIT-AGGRO.blp]])
LSM:Register("icons", "UNIT-DEAD",		[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\UNIT-DEAD.blp]])
LSM:Register("icons", "VS",				[[Interface\AddOns\JiberishMedia_SuperVillain\Icons\VS.blp]])

--* Sounds
LSM:Register("sound", "Character Blast Off Large Chunks of Terrain gibs",	[[Interface\AddOns\JiberishMedia_SuperVillain\Sounds\Character Blast Off Large Chunks of Terrain gibs.ogg]])
LSM:Register("sound", "Enhanced - SSJ3 100 - 75 percent Ki AURA",			[[Interface\AddOns\JiberishMedia_SuperVillain\Sounds\Enhanced - SSJ3 100 - 75 percent Ki AURA.ogg]])

--* Statusbars
LSM:Register("statusbar", "PATTERN1",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN1.blp]])
LSM:Register("statusbar", "PATTERN2",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN2.blp]])
LSM:Register("statusbar", "PATTERN3",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN3.blp]])
LSM:Register("statusbar", "PATTERN4",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN4.blp]])
LSM:Register("statusbar", "PATTERN5",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN5.blp]])
LSM:Register("statusbar", "PATTERN6",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN6.blp]])
LSM:Register("statusbar", "PATTERN7",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN7.blp]])
LSM:Register("statusbar", "PATTERN8",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN8.blp]])
LSM:Register("statusbar", "PATTERN9",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN9.blp]])
LSM:Register("statusbar", "PATTERN10",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN10.blp]])
LSM:Register("statusbar", "PATTERN11",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN11.blp]])
LSM:Register("statusbar", "PATTERN12",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN12.blp]])
LSM:Register("statusbar", "PATTERN13",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN13.blp]])
LSM:Register("statusbar", "PATTERN14",	[[Interface\AddOns\JiberishMedia_SuperVillain\Statusbars\PATTERN14.blp]])

--* Textures
LSM:Register("textures", "AFK-NARRATIVE",	[[Interface\AddOns\JiberishMedia_SuperVillain\Textures\AFK-NARRATIVE.blp]])
LSM:Register("textures", "ALERT-FULL",		[[Interface\AddOns\JiberishMedia_SuperVillain\Textures\ALERT-FULL.blp]])
LSM:Register("textures", "DRUNK-PARTYTIME", [[Interface\AddOns\JiberishMedia_SuperVillain\Textures\DRUNK-PARTYTIME.blp]])
LSM:Register("textures", "COMICS-TYPE1",	[[Interface\AddOns\JiberishMedia_SuperVillain\Textures\COMICS-TYPE1.blp]])
LSM:Register("textures", "COMICS-TYPE2",	[[Interface\AddOns\JiberishMedia_SuperVillain\Textures\COMICS-TYPE2.blp]])
LSM:Register("textures", "COMICS-TYPE3-BG", [[Interface\AddOns\JiberishMedia_SuperVillain\Textures\COMICS-TYPE3-BG.blp]])
LSM:Register("textures", "COMICS-TYPE3",	[[Interface\AddOns\JiberishMedia_SuperVillain\Textures\COMICS-TYPE3.blp]])
LSM:Register("textures", "QUESTION",		[[Interface\AddOns\JiberishMedia_SuperVillain\Textures\QUESTION.blp]])
LSM:Register("textures", "RESPONSE",		[[Interface\AddOns\JiberishMedia_SuperVillain\Textures\RESPONSE.blp]])
LSM:Register("textures", "SAVED-BG",		[[Interface\AddOns\JiberishMedia_SuperVillain\Textures\SAVED-BG.blp]])
