local LSM = LibStub("LibSharedMedia-3.0") 

-- ----- 
-- BACKGROUND 
-- ----- 
LSM:Register("background", "ART1", [[Interface\Addons\SharedMedia_MyMedia\background\ART1.blp]]) 
LSM:Register("background", "ART2", [[Interface\Addons\SharedMedia_MyMedia\background\ART2.blp]]) 
LSM:Register("background", "ART3", [[Interface\Addons\SharedMedia_MyMedia\background\ART3.blp]]) 
LSM:Register("background", "ART4", [[Interface\Addons\SharedMedia_MyMedia\background\ART4.blp]]) 
LSM:Register("background", "ART5", [[Interface\Addons\SharedMedia_MyMedia\background\ART5.blp]]) 
LSM:Register("background", "ART6", [[Interface\Addons\SharedMedia_MyMedia\background\ART6.blp]]) 


-- ----- 
--  BORDER 
-- ---- 

-- -----
--   FONT
-- -----
LSM:Register("font", "GothamNarrowUltra", [[Interface\Addons\SharedMedia_MyMedia\font\GothamNarrowUltra.ttf]]) 
LSM:Register("font", "Alert", [[Interface\Addons\SharedMedia_MyMedia\font\Alert.ttf]])
LSM:Register("font", "Boom", [[Interface\Addons\SharedMedia_MyMedia\font\Boom.ttf]])
LSM:Register("font", "Combat", [[Interface\Addons\SharedMedia_MyMedia\font\Combat.ttf]])
LSM:Register("font", "Combat2", [[Interface\Addons\SharedMedia_MyMedia\font\Combat2.ttf]])
LSM:Register("font", "Dialog", [[Interface\Addons\SharedMedia_MyMedia\font\Dialog.ttf]])
LSM:Register("font", "Invisible", [[Interface\Addons\SharedMedia_MyMedia\font\Invisible.ttf]])
LSM:Register("font", "Pixel", [[Interface\Addons\SharedMedia_MyMedia\font\Pixel.ttf]])
LSM:Register("font", "DejaVuSans", [[Interface\Addons\SharedMedia_MyMedia\font\DejaVuSans.ttf]])
LSM:Register("font", "Dyslexic", [[Interface\Addons\SharedMedia_MyMedia\font\Dyslexic.ttf]])
LSM:Register("font", "Carter", [[Interface\Addons\SharedMedia_MyMedia\font\Carter.ttf]])
LSM:Register("font", "Poppins-Black", [[Interface\Addons\SharedMedia_MyMedia\font\Poppins-Black.ttf]])
LSM:Register("font", "Poppins-ExtraBold", [[Interface\Addons\SharedMedia_MyMedia\font\Poppins-ExtraBold.ttf]])
LSM:Register("font", "Poppins-Regular", [[Interface\Addons\SharedMedia_MyMedia\font\Poppins-Regular.ttf]])

-- -----
--   SOUND
-- -----
LSM:Register("sound", "Character Blast Off Large Chunks of Terrain gibs", [[Interface\Addons\SharedMedia_MyMedia\sound\Character Blast Off Large Chunks of Terrain gibs.ogg]]) 
LSM:Register("sound", "Enhanced - SSJ3 100 - 75 percent Ki AURA ", [[Interface\Addons\SharedMedia_MyMedia\sound\Enhanced - SSJ3 100 - 75 percent Ki AURA .ogg]]) 

-- -----
--   STATUSBAR
-- -----
LSM:Register("statusbar", "PATTERN1", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN1.blp]])
LSM:Register("statusbar", "PATTERN2", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN2.blp]])
LSM:Register("statusbar", "PATTERN3", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN3.blp]])
LSM:Register("statusbar", "PATTERN4", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN4.blp]])
LSM:Register("statusbar", "PATTERN5", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN5.blp]])
LSM:Register("statusbar", "PATTERN6", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN6.blp]])
LSM:Register("statusbar", "PATTERN7", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN7.blp]])
LSM:Register("statusbar", "PATTERN8", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN8.blp]])
LSM:Register("statusbar", "PATTERN9", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN9.blp]])
LSM:Register("statusbar", "PATTERN10", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN10.blp]])
LSM:Register("statusbar", "PATTERN11", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN11.blp]])
LSM:Register("statusbar", "PATTERN12", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN12.blp]])
LSM:Register("statusbar", "PATTERN13", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN13.blp]])
LSM:Register("statusbar", "PATTERN14", [[Interface\Addons\SharedMedia_MyMedia\statusbar\PATTERN14.blp]])


-- ----- 
--  ICONS 
-- ---- 
LSM:Register("icons", "CLOSE", [[Interface\Addons\SharedMedia_MyMedia\icons\CLOSE.blp]])
LSM:Register("icons", "EXIT", [[Interface\Addons\SharedMedia_MyMedia\icons\EXIT.blp]])
LSM:Register("icons", "FAVORITE-STAR", [[Interface\Addons\SharedMedia_MyMedia\icons\FAVORITE-STAR.blp]])
LSM:Register("icons", "MOVE-DOWN", [[Interface\Addons\SharedMedia_MyMedia\icons\MOVE-DOWN.blp]])
LSM:Register("icons", "MOVE-LEFT", [[Interface\Addons\SharedMedia_MyMedia\icons\MOVE-LEFT.blp]])
LSM:Register("icons", "MOVE-RIGHT", [[Interface\Addons\SharedMedia_MyMedia\icons\MOVE-RIGHT.blp]])
LSM:Register("icons", "MOVE-UP", [[Interface\Addons\SharedMedia_MyMedia\icons\MOVE-UP.blp]])
LSM:Register("icons", "SVUI", [[Interface\Addons\SharedMedia_MyMedia\icons\SVUI.blp]])
LSM:Register("icons", "THEME", [[Interface\Addons\SharedMedia_MyMedia\icons\THEME.blp]])
LSM:Register("icons", "VS", [[Interface\Addons\SharedMedia_MyMedia\icons\VS.blp]])
LSM:Register("icons", "UNIT-DEAD", [[Interface\Addons\SharedMedia_MyMedia\icons\UNIT-DEAD.blp]])
LSM:Register("icons", "UNIT-AGGRO", [[Interface\Addons\SharedMedia_MyMedia\icons\UNIT-AGGRO.blp]])
LSM:Register("icons", "Hourglass", [[Interface\Addons\SharedMedia_MyMedia\icons\Hourglass.blp]])
LSM:Register("icons", "Combat", [[Interface\Addons\SharedMedia_MyMedia\icons\Combat]])

-- -----
--   TEXTURES
-- -----

LSM:Register("textures", "QUESTION", [[Interface\Addons\SharedMedia_MyMedia\textures\QUESTION.blp]])
LSM:Register("textures", "RESPONSE", [[Interface\Addons\SharedMedia_MyMedia\textures\RESPONSE.blp]])
LSM:Register("textures", "SAVED-BG", [[Interface\Addons\SharedMedia_MyMedia\textures\SAVED-BG.blp]])
LSM:Register("textures", "ALERT-FULL", [[Interface\Addons\SharedMedia_MyMedia\textures\ALERT-FULL.blp]])
LSM:Register("textures", "AFK-NARRATIVE", [[Interface\Addons\SharedMedia_MyMedia\textures\AFK-NARRATIVE.blp]])
LSM:Register("textures", "DRUNK-PARTYTIME", [[Interface\Addons\SharedMedia_MyMedia\textures\DRUNK-PARTYTIME.blp]])
LSM:Register("textures", "COMICS-TYPE3", [[Interface\Addons\SharedMedia_MyMedia\textures\COMICS-TYPE3.blp]])
LSM:Register("textures", "COMICS-TYPE2", [[Interface\Addons\SharedMedia_MyMedia\textures\COMICS-TYPE2.blp]])
LSM:Register("textures", "COMICS-TYPE3", [[Interface\Addons\SharedMedia_MyMedia\textures\COMICS-TYPE3.blp]])
LSM:Register("textures", "COMICS-TYPE3-BG", [[Interface\Addons\SharedMedia_MyMedia\textures\COMICS-TYPE3-BG.blp]])
