# Self-elevate the script if required
if (-Not ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] 'Administrator')) {
	if ([int](Get-CimInstance -Class Win32_OperatingSystem | Select-Object -ExpandProperty BuildNumber) -ge 6000) {
      $CommandLine = "-File `"" + $MyInvocation.MyCommand.Path + "`" " + $MyInvocation.UnboundArguments
      Start-Process -FilePath PowerShell.exe -Verb Runas -ArgumentList $CommandLine
      Exit
	}
}

$ProjectPath = "E:\Projects\WoW Related\Paid Projects\Jiberish\SuperVillain"
$addonpath = "Interface\AddOns"

$retail = "D:\Program Files\World of Warcraft\_retail_"
$cata = "D:\Program Files\World of Warcraft\_classic_"
$classic = "D:\Program Files\World of Warcraft\_classic_era_"
$mopbeta = "D:\Program Files\World of Warcraft\_classic_beta_"

##############################
#*     Retail ($retail)     *#
##############################
New-Item -ItemType SymbolicLink -Path $retail\$addonpath\JiberishUI_SuperVillain -Target $ProjectPath\JiberishUI_SuperVillain\JiberishUI_SuperVillain -Force
New-Item -ItemType SymbolicLink -Path $retail\$addonpath\JiberishMedia_SuperVillain -Target $ProjectPath\JiberishUI_SuperVillain\JiberishMedia_SuperVillain -Force

##########################
#*     Cata ($cata)     *#
##########################
New-Item -ItemType SymbolicLink -Path $cata\$addonpath\JiberishUI_SuperVillain -Target $ProjectPath\JiberishUI_SuperVillain\JiberishUI_SuperVillain -Force
New-Item -ItemType SymbolicLink -Path $cata\$addonpath\JiberishMedia_SuperVillain -Target $ProjectPath\JiberishUI_SuperVillain\JiberishMedia_SuperVillain -Force

########################################
#*     Classic Era/SoM ($classic)     *#
########################################
New-Item -ItemType SymbolicLink -Path $classic\$addonpath\JiberishUI_SuperVillain -Target $ProjectPath\JiberishUI_SuperVillain\JiberishUI_SuperVillain -Force
New-Item -ItemType SymbolicLink -Path $classic\$addonpath\JiberishMedia_SuperVillain -Target $ProjectPath\JiberishUI_SuperVillain\JiberishMedia_SuperVillain -Force

######################
#*     MOP Beta     *#
######################
New-Item -ItemType SymbolicLink -Path $mopbeta\$addonpath\JiberishUI_SuperVillain -Target $ProjectPath\JiberishUI_SuperVillain\JiberishUI_SuperVillain -Force
New-Item -ItemType SymbolicLink -Path $mopbeta\$addonpath\JiberishMedia_SuperVillain -Target $ProjectPath\JiberishUI_SuperVillain\JiberishMedia_SuperVillain -Force

Get-ChildItem $retail\$addonpath -Recurse -Force -ErrorAction SilentlyContinue | Where-Object {$_.PSIsContainer -and $_.Attributes -match "ReparsePoint"} | ForEach-Object {$_.Attributes = "ReparsePoint, ReadOnly"}
Get-ChildItem $cata\$addonpath -Recurse -Force -ErrorAction SilentlyContinue | Where-Object {$_.PSIsContainer -and $_.Attributes -match "ReparsePoint"} | ForEach-Object {$_.Attributes = "ReparsePoint, ReadOnly"}
Get-ChildItem $classic\$addonpath -Recurse -Force -ErrorAction SilentlyContinue | Where-Object {$_.PSIsContainer -and $_.Attributes -match "ReparsePoint"} | ForEach-Object {$_.Attributes = "ReparsePoint, ReadOnly"}
Get-ChildItem $mopbeta\$addonpath -Recurse -Force -ErrorAction SilentlyContinue | Where-Object {$_.PSIsContainer -and $_.Attributes -match "ReparsePoint"} | ForEach-Object {$_.Attributes = "ReparsePoint, ReadOnly"}
